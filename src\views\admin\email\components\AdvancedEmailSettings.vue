<template>
  <el-dialog
    :visible="advancedSettingsVisible"
    :append-to-body="true"
    :close-on-click-modal="false"
    width="800px"
    custom-class="no-padding-dialog"
    @close="closeClick">
    <span slot="title" class="el-dialog__title">添加邮箱账号</span>
    <div v-loading="loading" class="advanced-email-settings">
      <!-- 基本信息 -->
      <div class="section">
        <h3 class="section-title">基本信息</h3>
        <div class="form-grid">
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">邮箱账户：</label>
              <el-input
                v-model="formData.emailAccount"
                placeholder="请输入内容"
                class="form-input" />
            </div>
            <div class="form-item">
              <label class="form-label">账户类型：</label>
              <el-select
                v-model="formData.accountType"
                placeholder="个人"
                class="form-input">
                <el-option label="个人" value="personal" />
                <el-option label="公共" value="public" />
              </el-select>
            </div>
          </div>
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">邮箱密码：</label>
              <el-input
                v-model="formData.emailPassword"
                type="password"
                placeholder="请输入内容"
                class="form-input" />
            </div>
            <div class="form-item">
              <label class="form-label">拥有人：</label>
              <el-select
                v-model="formData.owner"
                placeholder="钟秋玲"
                class="form-input">
                <el-option label="钟秋玲" value="zhongqiuling" />
                <el-option label="其他用户" value="other" />
              </el-select>
            </div>
          </div>
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">显示名字：</label>
              <el-input
                v-model="formData.displayName"
                placeholder="请输入内容"
                class="form-input" />
            </div>
            <div class="form-item">
              <label class="form-label">回复地址：</label>
              <el-input
                v-model="formData.replyAddress"
                placeholder="请输入内容"
                class="form-input" />
            </div>
          </div>
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">邮箱协议：</label>
              <el-select
                v-model="formData.protocol"
                placeholder="POP"
                class="form-input">
                <el-option label="POP" value="pop" />
                <el-option label="IMAP" value="imap" />
              </el-select>
            </div>
          </div>
        </div>
      </div>

      <!-- 收信设置 -->
      <div class="section">
        <h3 class="section-title">收信设置</h3>
        <div class="section-description">当前邮箱仅作为发件邮箱，无需收信</div>
        
        <div class="form-item">
          <label class="form-label">接收账户：</label>
          <el-input
            v-model="formData.receiveAccount"
            placeholder="请输入内容"
            class="form-input-full" />
        </div>

        <div class="form-item">
          <label class="form-label">保存邮件副本：</label>
          <el-radio-group v-model="formData.saveEmailCopy" class="radio-group">
            <el-radio label="forever">
              <span class="radio-dot forever"></span>
              永久保留
            </el-radio>
            <el-radio label="10days">保留10天</el-radio>
            <el-radio label="30days">保留30天</el-radio>
            <el-radio label="60days">保留60天</el-radio>
            <el-radio label="none">不保留副本</el-radio>
          </el-radio-group>
        </div>

        <div class="form-item">
          <label class="form-label">接收邮件范围：</label>
          <div class="checkbox-group">
            <el-checkbox v-model="formData.receiveOptions.imapSent">
              使用IMAP接收已发件箱邮件
            </el-checkbox>
            <el-checkbox v-model="formData.receiveOptions.imapCustom">
              使用IMAP接收自定义文件夹邮件
            </el-checkbox>
            <el-checkbox v-model="formData.receiveOptions.usePredict" class="predict-option">
              使用预知
            </el-checkbox>
            <el-checkbox v-model="formData.receiveOptions.imapTrashPending">
              使用IMAP接收垃圾箱建档客户邮件(邮件保存于待处理)
            </el-checkbox>
            <el-checkbox v-model="formData.receiveOptions.imapTrashArchive">
              使用IMAP接收垃圾箱建档客户邮件(邮件保存于垃圾箱)
            </el-checkbox>
          </div>
        </div>

        <div class="form-item">
          <label class="form-label">收件间隔</label>
          <div class="interval-control">
            <el-button
              size="mini"
              icon="el-icon-minus"
              @click="decreaseInterval" />
            <span class="interval-value">{{ formData.receiveInterval }}</span>
            <el-button
              size="mini"
              icon="el-icon-plus"
              @click="increaseInterval" />
            <span class="interval-unit">分钟</span>
          </div>
        </div>
      </div>

      <!-- 发信设置 -->
      <div class="section">
        <h3 class="section-title">发信设置</h3>
        
        <div class="form-item">
          <label class="form-label">发信认证：</label>
          <el-select
            v-model="formData.sendAuth"
            placeholder="发信账户密码与收件相同（默认）"
            class="form-input-full">
            <el-option label="发信账户密码与收件相同（默认）" value="same" />
            <el-option label="使用不同的认证" value="different" />
          </el-select>
        </div>

        <div class="form-row">
          <div class="form-item">
            <label class="form-label">收信服务器：</label>
            <div class="server-input-group">
              <el-input
                v-model="formData.receiveServer"
                placeholder="请输入内容"
                class="server-input" />
              <el-checkbox v-model="formData.receiveSSL" class="ssl-checkbox">SSL</el-checkbox>
              <span class="port-label">端口：</span>
              <el-input
                v-model="formData.receivePort"
                placeholder="请输入内容"
                class="port-input" />
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-item">
            <label class="form-label">SMTP服务器：</label>
            <div class="server-input-group">
              <el-input
                v-model="formData.smtpServer"
                placeholder="请输入内容"
                class="server-input" />
              <el-checkbox v-model="formData.smtpSSL" class="ssl-checkbox">SSL</el-checkbox>
              <span class="port-label">端口：</span>
              <el-input
                v-model="formData.smtpPort"
                placeholder="请输入内容"
                class="port-input" />
            </div>
          </div>
        </div>

        <div class="form-item">
          <el-checkbox v-model="formData.useStartTLS">
            如果服务器支持就使用STARTTLS加密传输
          </el-checkbox>
        </div>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <div class="footer-content">
        <el-button
          type="primary"
          class="save-btn"
          @click="handleSave">保存</el-button>
        <el-button
          type="text"
          class="back-link"
          @click="handleBackToQuick">返回到快速添加</el-button>
      </div>
      <div class="help-section">
        <span class="help-title">帮助</span>
        <el-button
          type="text"
          class="help-link"
          @click="handleHelpClick">常见邮箱的绑定方法</el-button>
      </div>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'AdvancedEmailSettings',
  props: {
    advancedSettingsVisible: {
      type: Boolean,
      default: false
    },
    initialData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      formData: {
        emailAccount: '',
        emailPassword: '',
        displayName: '',
        protocol: 'pop',
        accountType: 'personal',
        owner: 'zhongqiuling',
        replyAddress: '',
        receiveAccount: '',
        saveEmailCopy: 'forever',
        receiveOptions: {
          imapSent: false,
          imapCustom: false,
          usePredict: false,
          imapTrashPending: false,
          imapTrashArchive: false
        },
        receiveInterval: 3,
        sendAuth: 'same',
        receiveServer: '',
        receiveSSL: false,
        receivePort: '',
        smtpServer: '',
        smtpSSL: false,
        smtpPort: '',
        useStartTLS: false
      }
    }
  },
  watch: {
    advancedSettingsVisible(value) {
      if (value) {
        this.initFormData()
      }
    }
  },
  methods: {
    /**
     * 初始化表单数据
     */
    initFormData() {
      if (this.initialData.emailAccount) {
        this.formData.emailAccount = this.initialData.emailAccount
      }
      if (this.initialData.emailPassword) {
        this.formData.emailPassword = this.initialData.emailPassword
      }
    },

    /**
     * 减少收件间隔
     */
    decreaseInterval() {
      if (this.formData.receiveInterval > 1) {
        this.formData.receiveInterval--
      }
    },

    /**
     * 增加收件间隔
     */
    increaseInterval() {
      this.formData.receiveInterval++
    },

    /**
     * 保存设置
     */
    handleSave() {
      if (!this.formData.emailAccount) {
        this.$message.error('请输入邮箱账户')
        return
      }
      if (!this.formData.emailPassword) {
        this.$message.error('请输入邮箱密码')
        return
      }

      this.loading = true
      // 这里可以调用保存邮箱高级设置的API
      setTimeout(() => {
        this.loading = false
        this.$message.success('邮箱高级设置保存成功')
        this.$emit('success', this.formData)
        this.closeClick()
      }, 1000)
    },

    /**
     * 返回到快速添加
     */
    handleBackToQuick() {
      this.$emit('back-to-quick')
      this.closeClick()
    },

    /**
     * 帮助链接点击
     */
    handleHelpClick() {
      this.$message.info('正在跳转到帮助页面...')
    },

    /**
     * 关闭弹框
     */
    closeClick() {
      this.$emit('update:advancedSettingsVisible', false)
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.advanced-email-settings {
  padding: 20px;
  max-height: 600px;
  overflow-y: auto;
}

.section {
  margin-bottom: 30px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 15px;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 8px;
  }

  .section-description {
    font-size: 14px;
    color: #718096;
    margin-bottom: 15px;
  }
}

.form-grid {
  .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;

    .form-item {
      flex: 1;
    }
  }
}

.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;

  .form-label {
    width: 100px;
    flex-shrink: 0;
    text-align: right;
    margin-right: 15px;
    font-size: 14px;
    color: #2d3748;
  }

  .form-input {
    flex: 1;
  }

  .form-input-full {
    width: 300px;
  }
}

.radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  ::v-deep .el-radio {
    margin-right: 0;
    
    .el-radio__label {
      font-size: 14px;
      color: #2d3748;
    }
  }

  .radio-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;

    &.forever {
      background-color: #0052CC;
    }
  }
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 10px;

  ::v-deep .el-checkbox {
    .el-checkbox__label {
      font-size: 14px;
      color: #2d3748;
    }
  }

  .predict-option {
    ::v-deep .el-checkbox__label {
      color: #3182ce;
    }
  }
}

.interval-control {
  display: flex;
  align-items: center;
  gap: 10px;

  .interval-value {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    min-width: 20px;
    text-align: center;
  }

  .interval-unit {
    font-size: 14px;
    color: #718096;
  }
}

.server-input-group {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;

  .server-input {
    flex: 1;
  }

  .ssl-checkbox {
    ::v-deep .el-checkbox__label {
      font-size: 14px;
      color: #2d3748;
    }
  }

  .port-label {
    font-size: 14px;
    color: #718096;
  }

  .port-input {
    width: 100px;
  }
}

.dialog-footer {
  display: flex;
  flex-direction: column;
  gap: 15px;

  .footer-content {
    display: flex;
    align-items: center;
    gap: 20px;

    .save-btn {
      background-color: #0052CC;
      border-color: #0052CC;
      color: white;
      padding: 10px 30px;

      &:hover {
        background-color: #c53030;
        border-color: #c53030;
      }
    }

    .back-link {
      color: #0052CC;
      font-size: 14px;
      padding: 0;

      &:hover {
        color: #c53030;
      }
    }
  }

  .help-section {
    display: flex;
    align-items: center;
    gap: 10px;
    padding-top: 15px;
    border-top: 1px solid #e2e8f0;

    .help-title {
      font-size: 14px;
      color: #718096;
    }

    .help-link {
      color: #3182ce;
      font-size: 14px;
      padding: 0;

      &:hover {
        color: #2c5aa0;
      }
    }
  }
}
</style>
