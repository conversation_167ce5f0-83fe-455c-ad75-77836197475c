<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Icon Test</title>
  <style>
    @font-face {
      font-family: 'wk-icon';
      src: url('../src/assets/fonts/wk-icon.eot?t=1624512994292');
      src: url('../src/assets/fonts/wk-icon.eot?t=1624512994292#iefix') format('embedded-opentype'),
           url('../src/assets/fonts/wk-icon.woff2?t=1624512994292') format('woff2'),
           url('../src/assets/fonts/wk-icon.woff?t=1624512994292') format('woff'),
           url('../src/assets/fonts/wk-icon.ttf?t=1624512994292') format('truetype'),
           url('../src/assets/fonts/wk-icon.svg?t=1624512994292#wk-icon') format('svg');
      font-weight: normal;
      font-style: normal;
    }

    .wk {
      font-family: "wk-icon" !important;
      font-size: 24px;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      margin-right: 10px;
    }

    /* 回复 */
    .wk-reply:before {
      content: "\e900";
    }

    /* 回复全部 */
    .wk-reply-all:before {
      content: "\e901";
    }

    /* 转发 */
    .wk-forward:before {
      content: "\e902";
    }

    /* 归档 */
    .wk-archive:before {
      content: "\e903";
    }

    /* 标签 */
    .wk-tag:before {
      content: "\e904";
    }

    /* 删除 */
    .wk-delete:before {
      content: "\e905";
    }

    /* 翻译 */
    .wk-translate:before {
      content: "\e906";
    }

    /* 星标 */
    .wk-star:before {
      content: "\e907";
    }

    /* 更多 */
    .wk-more:before {
      content: "\e908";
    }

    /* 网格 */
    .wk-grid:before {
      content: "\e909";
    }

    /* 用户 */
    .wk-user:before {
      content: "\e90a";
    }

    /* 退出登录 */
    .wk-logout:before {
      content: "\e90b";
    }

    /* 版本 */
    .wk-version:before {
      content: "\e90c";
    }

    /* 搜索 */
    .wk-sousuo:before {
      content: "\e90d";
    }

    .icon-container {
      display: flex;
      flex-wrap: wrap;
      margin: 20px;
    }

    .icon-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 10px;
      width: 100px;
    }

    .icon-name {
      font-size: 12px;
      margin-top: 5px;
    }
  </style>
</head>
<body>
  <h1>Icon Test</h1>
  
  <div class="icon-container">
    <div class="icon-item">
      <i class="wk wk-reply"></i>
      <span class="icon-name">wk-reply</span>
    </div>
    <div class="icon-item">
      <i class="wk wk-reply-all"></i>
      <span class="icon-name">wk-reply-all</span>
    </div>
    <div class="icon-item">
      <i class="wk wk-forward"></i>
      <span class="icon-name">wk-forward</span>
    </div>
    <div class="icon-item">
      <i class="wk wk-archive"></i>
      <span class="icon-name">wk-archive</span>
    </div>
    <div class="icon-item">
      <i class="wk wk-tag"></i>
      <span class="icon-name">wk-tag</span>
    </div>
    <div class="icon-item">
      <i class="wk wk-delete"></i>
      <span class="icon-name">wk-delete</span>
    </div>
    <div class="icon-item">
      <i class="wk wk-translate"></i>
      <span class="icon-name">wk-translate</span>
    </div>
    <div class="icon-item">
      <i class="wk wk-star"></i>
      <span class="icon-name">wk-star</span>
    </div>
    <div class="icon-item">
      <i class="wk wk-more"></i>
      <span class="icon-name">wk-more</span>
    </div>
    <div class="icon-item">
      <i class="wk wk-grid"></i>
      <span class="icon-name">wk-grid</span>
    </div>
    <div class="icon-item">
      <i class="wk wk-user"></i>
      <span class="icon-name">wk-user</span>
    </div>
    <div class="icon-item">
      <i class="wk wk-logout"></i>
      <span class="icon-name">wk-logout</span>
    </div>
    <div class="icon-item">
      <i class="wk wk-version"></i>
      <span class="icon-name">wk-version</span>
    </div>
    <div class="icon-item">
      <i class="wk wk-sousuo"></i>
      <span class="icon-name">wk-sousuo</span>
    </div>
  </div>
</body>
</html>
