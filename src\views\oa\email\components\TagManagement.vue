<template>
  <div class="modal-overlay" v-if="visible" @click="closeModal">
    <div class="modal-content tag-management-modal" @click.stop>
      <div class="modal-header">
        <h3>标签管理</h3>
        <button class="close-modal" @click="closeModal">
          <XIcon class="icon-small" />
        </button>
      </div>
      <div class="modal-body">
        <!-- 已选标签区域 -->
        <div class="selected-tags-area">
          <div
            v-for="(tag, index) in localSelectedTags"
            :key="`selected-${tag.id}`"
            class="selected-tag-item"
            :class="tag.type === 'custom' ? tag.colorClass : 'system'"
          >
            <span>{{ tag.name }}</span>
            <XIcon class="icon-tiny" @click="removeSelectedTag(tag)" />
          </div>
          <!-- 搜索框 -->
          <div class="tag-search-box">
            <input type="text" placeholder="请输入标签" v-model="searchTerm" />
            <SearchIcon class="icon-tiny search-icon" />
          </div>
        </div>

        <div class="tag-divider"></div>

        <!-- 系统标签区域 -->
        <div class="tag-section">
          <h4 class="tag-section-title">系统标签</h4>
          <div class="tag-list">
            <div
              v-for="(tag, index) in filteredSystemTags"
              :key="`system-${index}`"
              class="tag-item system-tag"
              @click="toggleTagSelection(tag)"
            >
              <span>{{ tag.name }}</span>
            </div>
          </div>
        </div>

        <div class="tag-divider"></div>

        <!-- 自定义标签区域 -->
        <div class="tag-section">
          <h4 class="tag-section-title">自定义标签</h4>
          <div class="tag-list">
            <div
              v-for="(tag, index) in filteredCustomTags"
              :key="`custom-${index}`"
              class="tag-item custom-tag"
              :class="tag.colorClass"
              @click="toggleTagSelection(tag)"
            >
              <span>{{ tag.name }}</span>
              <EditIcon class="icon-tiny edit-icon" @click.stop="editTag(tag)" />
            </div>
            <div class="tag-item custom-tag empty">
              <input
                type="text"
                placeholder="添加自定义标签"
                class="new-tag-input"
                v-model="newTagName"
                @keyup.enter="addNewTag"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="cancel-btn" @click="closeModal">取消</button>
        <button class="save-btn" @click="saveChanges">保存</button>
      </div>
    </div>
  </div>
</template>

<script>
import { X, Search, Edit } from 'lucide-vue';

export default {
  name: 'TagManagement',
  components: {
    XIcon: X,
    SearchIcon: Search,
    EditIcon: Edit
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    tags: {
      type: Array,
      default: () => []
    },
    customTagsList: {
      type: Array,
      default: () => []
    },
    selectedTags: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      searchTerm: '',
      newTagName: '',
      localSelectedTags: [],
      systemTags: [
        { id: 'sys-1', name: '通知', type: 'system', colorClass: 'red' },
        { id: 'sys-2', name: '招聘', type: 'system', colorClass: 'red' },
        { id: 'sys-3', name: '商机', type: 'system', colorClass: 'red' },
        { id: 'sys-4', name: '报价', type: 'system', colorClass: 'red' },
        { id: 'sys-5', name: '已更回复', type: 'system', colorClass: 'red' },
        { id: 'sys-6', name: 'PI', type: 'system', colorClass: 'red' },
        { id: 'sys-7', name: '订单', type: 'system', colorClass: 'red' },
        { id: 'sys-8', name: '样品', type: 'system', colorClass: 'red' },
        { id: 'sys-9', name: '询盘', type: 'system', colorClass: 'red' }
      ],
      customTags: []
    };
  },
  computed: {
    filteredSystemTags() {
      if (!this.searchTerm) return this.systemTags;
      return this.systemTags.filter(tag =>
        tag.name.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    },
    filteredCustomTags() {
      if (!this.searchTerm) return this.customTags;
      return this.customTags.filter(tag =>
        tag.name.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 重置并初始化已选标签列表
        this.resetAndInitSelectedTags();

        // 初始化自定义标签列表
        this.initCustomTags();
      }
    },
    selectedTags: {
      handler(newVal) {
        // 当selectedTags属性变化时，重新初始化已选标签
        if (this.visible && newVal) {
          this.resetAndInitSelectedTags();
        }
      },
      deep: true
    },
    customTagsList: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.initCustomTags();
        }
      }
    }
  },
  methods: {
    closeModal() {
      this.$emit('close');
    },

    // 重置并初始化已选标签列表
    resetAndInitSelectedTags() {
      // 清空当前已选标签
      this.localSelectedTags = [];

      // 使用传入的selectedTags初始化
      if (this.selectedTags && this.selectedTags.length > 0) {
        // 深拷贝以避免引用问题
        this.localSelectedTags = JSON.parse(JSON.stringify(this.selectedTags));

        // 确保不会重复添加标签
        this.localSelectedTags = this.localSelectedTags.filter((tag, index, self) =>
          index === self.findIndex(t => t.id === tag.id)
        );
      }
    },

    // 初始化自定义标签列表
    initCustomTags() {
      if (this.customTagsList && this.customTagsList.length > 0) {
        // 将父组件传入的自定义标签转换为组件内部使用的格式
        this.customTags = this.customTagsList.map(tag => ({
          id: `custom-${tag.id}`,
          name: tag.name,
          type: 'custom',
          colorClass: this.getColorClassFromHex(tag.color)
        }));
      }
    },

    // 从十六进制颜色值获取CSS类名
    getColorClassFromHex(hexColor) {
      const colorMap = {
        '#4caf50': 'green',
        '#9c27b0': 'purple',
        '#795548': 'brown',
        '#00bcd4': 'cyan',
        '#ff9800': 'orange',
        '#e60012': 'red',
        '#2196f3': 'blue'
      };
      return colorMap[hexColor] || 'red';
    },
    toggleTagSelection(tag) {
      // 检查标签是否已经在已选列表中
      const index = this.localSelectedTags.findIndex(t => t.id === tag.id);
      if (index === -1) {
        // 如果标签不在已选列表中，添加它
        this.localSelectedTags.push({...tag});

        // 实时更新父组件中的标签选择
        this.$emit('tag-selected', this.localSelectedTags);
      }
      // 注意：这里不删除标签，因为我们希望点击标签只能添加，不能删除
    },

    // 从已选标签中移除标签
    removeSelectedTag(tag) {
      const index = this.localSelectedTags.findIndex(t => t.id === tag.id);
      if (index !== -1) {
        this.localSelectedTags.splice(index, 1);

        // 实时更新父组件中的标签选择
        this.$emit('tag-selected', this.localSelectedTags);
      }
    },

    // 根据标签ID从已选标签中移除标签（供外部调用）
    removeTagById(tagId) {
      let removed = false;

      // 系统标签ID (1-9)
      if (tagId >= 1 && tagId <= 9) {
        const sysTagId = `sys-${tagId}`;
        const index = this.localSelectedTags.findIndex(t => t.id === sysTagId);
        if (index !== -1) {
          this.localSelectedTags.splice(index, 1);
          removed = true;
        }
      } else {
        // 自定义标签
        const customTagId = `custom-${tagId}`;
        const index = this.localSelectedTags.findIndex(t => t.id === customTagId);
        if (index !== -1) {
          this.localSelectedTags.splice(index, 1);
          removed = true;
        }
      }

      // 如果有标签被移除，实时更新父组件
      if (removed) {
        this.$emit('tag-selected', this.localSelectedTags);
      }
    },
    editTag(tag) {
      this.$emit('edit-tag', tag);
    },
    addNewTag() {
      if (!this.newTagName.trim()) return;

      const newTag = {
        id: `custom-${Date.now()}`,
        name: this.newTagName.trim(),
        type: 'custom',
        colorClass: this.getRandomColorClass()
      };

      this.customTags.push(newTag);
      this.newTagName = '';
    },
    getRandomColorClass() {
      const colors = ['green', 'purple', 'brown', 'cyan', 'orange', 'red', 'blue'];
      return colors[Math.floor(Math.random() * colors.length)];
    },
    saveChanges() {
      // 确保所有选中的标签（系统和自定义）都会被保存
      this.$emit('save', {
        selectedTags: this.localSelectedTags,
        customTags: this.customTags
      });

      // 实时更新父组件中的标签选择
      this.$emit('tag-selected', this.localSelectedTags);

      this.closeModal();
    }
  }
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  width: 450px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
  border-radius: 8px 8px 0 0;
}

.modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.close-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  color: #909399;
  transition: all 0.2s;
}

.close-modal:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #606266;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
  border-radius: 0 0 8px 8px;
  gap: 12px;
}

.cancel-btn, .save-btn {
  padding: 9px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  border: none;
}

.cancel-btn {
  background-color: #f4f4f5;
  color: #606266;
  border: 1px solid #dcdfe6;
}

.cancel-btn:hover {
  background-color: #e9e9eb;
  color: #303133;
}

.save-btn {
  background-color: #1890ff;
  color: white;
  border: 1px solid #1890ff;
}

.save-btn:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 标签管理模态框样式 */
.tag-management-modal {
  width: 600px;
  max-width: 95%;
}

.selected-tags-area {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.selected-tag-item {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 16px;
  padding: 4px 10px;
  font-size: 13px;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 5px;
}

.selected-tag-item:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

/* 自定义标签颜色类 */
.selected-tag-item.green {
  background-color: #67c23a;
  color: white;
}

.selected-tag-item.purple {
  background-color: #9c27b0;
  color: white;
}

.selected-tag-item.brown {
  background-color: #795548;
  color: white;
}

.selected-tag-item.cyan {
  background-color: #00bcd4;
  color: white;
}

.selected-tag-item.orange {
  background-color: #ff9800;
  color: white;
}

.selected-tag-item.red {
  background-color: #f44336;
  color: white;
}

.selected-tag-item.blue {
  background-color: #2196f3;
  color: white;
}

.selected-tag-item .icon-tiny {
  margin-left: 6px;
  cursor: pointer;
  opacity: 0.7;
  transition: all 0.2s;
}

.selected-tag-item .icon-tiny:hover {
  opacity: 1;
  transform: scale(1.1);
}

.tag-search-box {
  position: relative;
  flex: 1;
  min-width: 200px;
}

.tag-search-box input {
  width: 100%;
  padding: 6px 30px 6px 10px;
  border: 1px solid #dcdfe6;
  border-radius: 16px;
  font-size: 13px;
}

.tag-search-box .search-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #909399;
}

.tag-divider {
  height: 1px;
  background-color: #ebeef5;
  margin: 16px 0;
}

.tag-section-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #606266;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag-item {
  display: flex;
  align-items: center;
  border-radius: 16px;
  padding: 5px 12px;
  font-size: 13px;
  cursor: pointer;
  position: relative;
}

.system-tag {
  background-color: #f0f0f0;
  color: #606266;
  transition: all 0.2s;
}

.system-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  filter: brightness(1.1);
}

/* 系统标签统一使用灰色 */
.system-tag {
  background-color: #f0f0f0;
  color: #606266;
}

/* 已选中的系统标签 */
.selected-tag-item.system {
  background-color: #f0f0f0;
  color: #606266;
}

.custom-tag {
  color: white;
  padding-right: 28px;
  transition: all 0.2s;
}

.custom-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  filter: brightness(1.1);
}

.custom-tag.green {
  background-color: #67c23a;
}

.custom-tag.purple {
  background-color: #9c27b0;
}

.custom-tag.brown {
  background-color: #795548;
}

.custom-tag.cyan {
  background-color: #00bcd4;
}

.custom-tag.orange {
  background-color: #ff9800;
}

.custom-tag.red {
  background-color: #f44336;
}

.custom-tag.blue {
  background-color: #2196f3;
}

.custom-tag .edit-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}

.custom-tag.empty {
  background-color: transparent;
  border: 1px dashed #dcdfe6;
  padding: 4px 12px;
}

.new-tag-input {
  background: transparent;
  border: none;
  outline: none;
  width: 120px;
  font-size: 13px;
}

.icon-small {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.icon-tiny {
  width: 12px;
  height: 12px;
  margin: 0 2px;
  cursor: pointer;
}
</style>
