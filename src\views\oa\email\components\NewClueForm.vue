<template>
  <div class="new-clue-form">
    <div class="form-header">
      <h2>新增线索</h2>
      <p>从邮件 "{{ email.subject }}" 创建线索</p>
    </div>
    
    <div class="form-body">
      <div class="form-group">
        <label>线索名称</label>
        <input type="text" v-model="clueData.name" placeholder="请输入线索名称" />
      </div>
      
      <div class="form-group">
        <label>公司名称</label>
        <input type="text" v-model="clueData.company" placeholder="请输入公司名称" />
      </div>
      
      <div class="form-group">
        <label>联系人</label>
        <input type="text" v-model="clueData.contact" placeholder="请输入联系人姓名" />
      </div>
      
      <div class="form-group">
        <label>联系电话</label>
        <input type="text" v-model="clueData.phone" placeholder="请输入联系电话" />
      </div>
      
      <div class="form-group">
        <label>邮箱</label>
        <input type="email" v-model="clueData.email" placeholder="请输入邮箱地址" />
      </div>
      
      <div class="form-group">
        <label>线索来源</label>
        <select v-model="clueData.source">
          <option value="email">邮件</option>
          <option value="website">网站</option>
          <option value="phone">电话</option>
          <option value="referral">推荐</option>
          <option value="other">其他</option>
        </select>
      </div>
      
      <div class="form-group">
        <label>线索状态</label>
        <select v-model="clueData.status">
          <option value="new">新建</option>
          <option value="following">跟进中</option>
          <option value="converted">已转化</option>
          <option value="invalid">无效</option>
        </select>
      </div>
      
      <div class="form-group">
        <label>备注</label>
        <textarea v-model="clueData.remarks" placeholder="请输入备注信息"></textarea>
      </div>
      
      <div class="form-group">
        <label>关联邮件</label>
        <div class="related-email">
          <div class="email-subject">{{ email.subject }}</div>
          <div class="email-sender">发件人: {{ email.sender }}</div>
          <div class="email-time">时间: {{ email.fullDate || email.time }}</div>
        </div>
      </div>
    </div>
    
    <div class="form-footer">
      <button class="cancel-btn" @click="cancel">取消</button>
      <button class="save-btn" @click="saveClue">保存</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NewClueForm',
  props: {
    email: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      clueData: {
        name: '',
        company: '',
        contact: '',
        phone: '',
        email: '',
        source: 'email',
        status: 'new',
        remarks: '',
        relatedEmailId: null
      }
    }
  },
  created() {
    // 预填充表单数据
    this.prefillFormData();
  },
  methods: {
    prefillFormData() {
      if (this.email) {
        // 从邮件中提取公司名称和联系人信息
        this.clueData.name = this.email.subject || '';
        
        if (this.email.tag) {
          this.clueData.company = this.email.tag;
        }
        
        this.clueData.contact = this.email.sender || '';
        this.clueData.email = this.email.sender ? `${this.email.sender.toLowerCase().replace(/\s+/g, '.')}@example.com` : '';
        this.clueData.relatedEmailId = this.email.id;
        
        // 提取邮件内容作为备注
        const emailContent = this.stripHtml(this.email.body || '');
        this.clueData.remarks = `来自邮件: ${this.email.subject}\n\n${emailContent.substring(0, 200)}${emailContent.length > 200 ? '...' : ''}`;
      }
    },
    
    stripHtml(html) {
      const tmp = document.createElement('div');
      tmp.innerHTML = html;
      return tmp.textContent || tmp.innerText || '';
    },
    
    saveClue() {
      // 模拟保存线索
      console.log('保存线索:', this.clueData);
      
      // 显示成功提示
      this.$emit('save-success', {
        ...this.clueData,
        id: Date.now()
      });
    },
    
    cancel() {
      this.$emit('cancel');
    }
  }
}
</script>

<style scoped>
.new-clue-form {
  padding: 20px;
  background-color: #fff;
  height: 100%;
  overflow: auto;
}

.form-header {
  margin-bottom: 24px;
  border-bottom: 1px solid #e6e9ed;
  padding-bottom: 16px;
}

.form-header h2 {
  font-size: 20px;
  margin-bottom: 8px;
  color: #333;
}

.form-header p {
  color: #666;
  font-size: 14px;
}

.form-body {
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.related-email {
  padding: 12px;
  background-color: #f5f7fa;
  border: 1px solid #e6e9ed;
  border-radius: 4px;
}

.email-subject {
  font-weight: 500;
  margin-bottom: 8px;
}

.email-sender,
.email-time {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.form-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #e6e9ed;
}

.cancel-btn,
.save-btn {
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

.cancel-btn {
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  color: #606266;
  margin-right: 12px;
}

.save-btn {
  background-color: #409EFF;
  border: 1px solid #409EFF;
  color: white;
}

.cancel-btn:hover {
  background-color: #e6e9ed;
}

.save-btn:hover {
  background-color: #66b1ff;
}
</style>
