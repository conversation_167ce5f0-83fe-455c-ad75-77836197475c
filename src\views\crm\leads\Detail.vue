<template>
  <slide-view
    v-empty="!canShowDetail"
    :listener-ids="listenerIDs"
    :no-listener-ids="noListenerIDs"
    :no-listener-class="noListenerClass"
    :body-style="{padding: 0, height: '100%'}"
    xs-empty-icon="nopermission"
    xs-empty-text="暂无权限"
    @afterEnter="viewAfterEnter"
    @close="hideView">
    <div
      ref="crmDetailMain"
      v-loading="loading"
      class="detail-main no-padding">
      <flexbox
        v-if="canShowDetail && detailData"
        direction="column"
        align="stretch"
        class="d-container">
        <c-r-m-detail-head
          :id="id"
          :class="{'is-shadow': bodyIsScroll}"
          :detail="detailData"
          :handles="activityHandle"
          :model-data="modelData"
          :crm-type="crmType"
          :page-list="pageList"
          :tag-info="tagInfo"
          @pageChange="pageChange"
          @handle="detailHeadHandle"
          @close="hideView">
          <template slot="name">
            <el-tooltip :content="detailData.star == 0 ? '添加关注' : '取消关注'" effect="dark" placement="top">
              <i
                v-if="detailData.star == 0"
                class="el-icon-star-off focus-icon"
                @click="toggleStar()" />
              <i
                v-else
                class="wk wk-focus-on focus-icon active"
                @click="toggleStar()" />
            </el-tooltip>
          </template>
        </c-r-m-detail-head>

        <div class="d-container-body" @scroll="bodyScroll">
          <detail-head-base :list="headDetails" />
          <relative-stage-records
            v-if="statusShow"
            :id="id"
            :crm-type="crmType"
            :detail="detailData"
            @handle="detailHeadHandle"
          />
          <el-tabs
            v-model="tabCurrentName"
            class="top-padding"
            nav-mode="more">
            <el-tab-pane
              v-for="(item, index) in tabNames"
              :key="index"
              :label="item.label"
              :name="item.name"
              lazy>
              <template slot="label">
                <el-badge
                  :value="item.num"
                  :hidden="item.num <= 0"
                  type="undefined">
                  {{ item.label }}
                </el-badge>
              </template>
              <!-- 工商信息 -->
              <business-info-view
                v-if="item.name === 'BusinessInfoView'"
                :name="detailData.leadsName"
                style="padding: 0;"
                :props="{ fillBtnShow: false, isFixed: false, style: {padding: 0, marginBottom: '16px'}, closeShow: false }"
              />
              <component
                :is="item.name"
                v-else
                :id="id"
                :ref="item.name"
                :detail="detailData"
                :type-list="logTyps"
                :handle="activityHandle"
                :crm-type="crmType"
                :ignore-fields="['leadsName']"
                @handle="detailHeadHandle" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </flexbox>
    </div>
    <c-r-m-all-create
      v-if="isCreate"
      :action="{type: 'update', id: id, batchId: detailData.batchId}"
      :crm-type="crmType"
      @save-success="editSaveSuccess"
      @close="isCreate=false" />
  </slide-view>
</template>

<script>
import { crmLeadsReadAPI } from '@/api/crm/leads'

import SlideView from '@/components/SlideView'
import CRMDetailHead from '../components/CRMDetailHead'
import RelativeStageRecords from '../components/RelativeStageRecords' // 阶段记录
import Activity from '../components/Activity'
import ImportInfo from '../components/ImportInfo' // 重要信息
import CRMEditBaseInfo from '../components/CRMEditBaseInfo' // 线索基本信息
import RelativeFiles from '../components/RelativeFiles' // 相关附件
import RelativeHandle from '../components/RelativeHandle' // 相关操作
import BusinessInfoView from '@/components/Premium/BusinessInfo/View'

import CRMAllCreate from '../components/CRMAllCreate' // 新建页面
import DetailMixin from '../mixins/Detail'

export default {
  // 线索管理 的 线索详情
  name: 'LeadsDetail',
  components: {
    SlideView,
    CRMDetailHead,
    RelativeStageRecords,
    Activity,
    CRMEditBaseInfo,
    RelativeFiles,
    RelativeHandle,
    ImportInfo,
    CRMAllCreate,
    BusinessInfoView
  },
  mixins: [DetailMixin],
  props: {
    // 详情信息id
    id: [String, Number],
    // 监听的dom 进行隐藏详情
    listenerIDs: {
      type: Array,
      default: () => {
        return ['crm-main-container']
      }
    },
    // 不监听
    noListenerIDs: {
      type: Array,
      default: () => {
        return []
      }
    },
    noListenerClass: {
      type: Array,
      default: () => {
        return ['el-table__body']
      }
    },
    /** 呼出信息 */
    modelData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      // 展示加载loading
      loading: false,
      crmType: 'leads',
      headDetails: [],
      tabCurrentName: 'Activity',
      // 编辑操作
      isCreate: false
    }
  },
  computed: {
    /**
     * 活动操作
     */
    activityHandle() {
      const temps = []

      // if (this.canCreateFollowRecord) {
      //   temps = [{
      //     type: 'log',
      //     label: '写跟进'
      //   }]
      // }

      return temps
    },
    /**
     * 根据记录筛选
     */
    logTyps() {
      let logTypslist = []
      logTypslist = [
        {
          icon: 'all',
          color: '#2362FB',
          command: '',
          label: '全部活动'
        }
      ]
      if (this.$store.state.crm.isCall) {
        logTypslist.push({
          icon: 'phone',
          color: '#9376FF',
          command: 13,
          label: '呼叫记录'
        })
      }
      return logTypslist
    },
    /**
     * tabs
     */
    tabNames() {
      const tempsTabs = [
        { label: '活动', name: 'Activity' },
        { label: '详细资料', name: 'CRMEditBaseInfo' },
        { label: '工商信息', name: 'BusinessInfoView' },
        { label: '附件', name: 'RelativeFiles', num: this.tabsNumber.fileCount },
        { label: '操作记录', name: 'RelativeHandle' }
      ]
      // if (this.statusShow) {
      //   tempsTabs.unshift({ label: '阶段记录', name: 'RelativeStageRecords' })
      // }
      return tempsTabs
    }
  },
  mounted() {},
  methods: {
    /**
     * 详情
     */
    getDetial() {
      this.loading = true
      crmLeadsReadAPI(this.id)
        .then(res => {
          const resData = res.data || {}
          this.detailData = resData

          this.headDetails = [
            { title: '线索来源', value: resData.source },
            { title: '手机', value: resData.mobile },
            { title: '负责人', value: resData.ownerUserName },
            { title: '创建时间', value: resData.createTime }
          ]
          this.loading = false
        })
        .catch(() => {
          this.loading = false
          this.hideView()
        })
    },

    /**
     * 关闭
     */
    hideView() {
      this.$emit('hide-view')
    }

    // /**
    //  * 编辑成功
    //  */
    // editSaveSuccess() {
    //   this.$emit('handle', { type: 'save-success' })
    //   this.getDetial()
    // }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/crmdetail.scss";

.import-info {
  height: 100%;
  overflow: auto;
}
</style>
