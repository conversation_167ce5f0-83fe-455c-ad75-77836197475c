<template>
  <div class="ai-assistant-drawer" :class="{ 'visible': visible }">
    <div class="drawer-header">
      <div class="header-title">
        <zap-icon class="icon-small" />
        AI 助手
      </div>
      <button class="close-btn" @click="$emit('close')">
        <x-icon class="icon-tiny" />
      </button>
    </div>

    <div class="drawer-content">
      <!-- 智能沟通模块 -->
      <div class="ai-module">
        <div class="module-header" @click="toggleModule('communication')">
          <message-circle-icon class="icon-small" />
          <span>智能沟通</span>
          <chevron-down-icon
            class="icon-tiny expand-icon"
            :class="{ 'expanded': expandedModules.communication }"
          />
        </div>

        <div class="module-content" v-show="expandedModules.communication">
          <div class="ai-function" @click="handleFunction('reply-suggestion')">
            <div class="function-icon">
              <reply-icon class="icon-small" />
            </div>
            <div class="function-info">
              <div class="function-title">智能回复建议</div>
              <div class="function-desc">基于邮件内容生成回复建议</div>
            </div>
          </div>

          <div class="ai-function" @click="handleFunction('tone-analysis')">
            <div class="function-icon">
              <heart-icon class="icon-small" />
            </div>
            <div class="function-info">
              <div class="function-title">语气分析</div>
              <div class="function-desc">分析邮件语气和情感倾向</div>
            </div>
          </div>

          <div class="ai-function" @click="handleFunction('follow-up')">
            <div class="function-icon">
              <clock-icon class="icon-small" />
            </div>
            <div class="function-info">
              <div class="function-title">跟进提醒</div>
              <div class="function-desc">智能建议最佳跟进时间</div>
            </div>
          </div>

          <div class="ai-function" @click="handleFunction('template-match')">
            <div class="function-icon">
              <file-text-icon class="icon-small" />
            </div>
            <div class="function-info">
              <div class="function-title">模板匹配</div>
              <div class="function-desc">推荐合适的回复模板</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 客户洞察模块 -->
      <div class="ai-module">
        <div class="module-header" @click="toggleModule('insights')">
          <users-icon class="icon-small" />
          <span>客户洞察</span>
          <chevron-down-icon
            class="icon-tiny expand-icon"
            :class="{ 'expanded': expandedModules.insights }"
          />
        </div>

        <div class="module-content" v-show="expandedModules.insights">
          <div class="ai-function" @click="handleFunction('customer-profile')">
            <div class="function-icon">
              <user-icon class="icon-small" />
            </div>
            <div class="function-info">
              <div class="function-title">客户画像</div>
              <div class="function-desc">分析客户特征和偏好</div>
            </div>
          </div>

          <div class="ai-function" @click="handleFunction('interaction-history')">
            <div class="function-icon">
              <history-icon class="icon-small" />
            </div>
            <div class="function-info">
              <div class="function-title">互动历史</div>
              <div class="function-desc">查看客户互动时间线</div>
            </div>
          </div>

          <div class="ai-function" @click="handleFunction('opportunity-analysis')">
            <div class="function-icon">
              <trending-up-icon class="icon-small" />
            </div>
            <div class="function-info">
              <div class="function-title">商机分析</div>
              <div class="function-desc">识别潜在商业机会</div>
            </div>
          </div>

          <div class="ai-function" @click="handleFunction('risk-assessment')">
            <div class="function-icon">
              <alert-triangle-icon class="icon-small" />
            </div>
            <div class="function-info">
              <div class="function-title">风险评估</div>
              <div class="function-desc">评估客户流失风险</div>
            </div>
          </div>
        </div>
      </div>

      <!-- AI分析结果显示区域 -->
      <div class="ai-result" v-if="showResult">
        <div class="result-header">
          <div class="result-title">
            <zap-icon class="icon-small" />
            {{ currentFunction.title }}
          </div>
          <button class="close-result-btn" @click="closeResult">
            <x-icon class="icon-tiny" />
          </button>
        </div>
        <div class="result-content">
          <div v-if="isLoading" class="result-loading">
            <rotate-cw-icon class="icon-small spin" />
            正在分析...
          </div>
          <div v-else class="result-text">
            {{ resultContent }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  Zap, X, MessageCircle, ChevronDown, Reply, Heart, Clock, FileText,
  Users, User, History, TrendingUp, AlertTriangle, RotateCw
} from 'lucide-vue'

export default {
  name: 'AiAssistantDrawer',
  components: {
    ZapIcon: Zap,
    XIcon: X,
    MessageCircleIcon: MessageCircle,
    ChevronDownIcon: ChevronDown,
    ReplyIcon: Reply,
    HeartIcon: Heart,
    ClockIcon: Clock,
    FileTextIcon: FileText,
    UsersIcon: Users,
    UserIcon: User,
    HistoryIcon: History,
    TrendingUpIcon: TrendingUp,
    AlertTriangleIcon: AlertTriangle,
    RotateCwIcon: RotateCw
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    email: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      expandedModules: {
        communication: true,
        insights: false
      },
      showResult: false,
      isLoading: false,
      resultContent: '',
      currentFunction: {}
    }
  },
  methods: {
    toggleModule(module) {
      this.expandedModules[module] = !this.expandedModules[module]
    },

    async handleFunction(functionType) {
      // 如果没有邮件，显示通用AI功能提示
      if (!this.email && ['reply-suggestion', 'tone-analysis', 'follow-up', 'template-match'].includes(functionType)) {
        this.showToast('此功能需要在邮件模块中使用', 'warning')
        return
      }

      this.currentFunction = this.getFunctionInfo(functionType)
      this.showResult = true
      this.isLoading = true

      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 2000))

        this.resultContent = this.generateMockResult(functionType)
      } catch (error) {
        this.resultContent = '分析失败，请稍后重试'
      } finally {
        this.isLoading = false
      }
    },

    getFunctionInfo(functionType) {
      const functions = {
        'reply-suggestion': { title: '智能回复建议', icon: 'reply' },
        'tone-analysis': { title: '语气分析', icon: 'heart' },
        'follow-up': { title: '跟进提醒', icon: 'clock' },
        'template-match': { title: '模板匹配', icon: 'file-text' },
        'customer-profile': { title: '客户画像', icon: 'user' },
        'interaction-history': { title: '互动历史', icon: 'history' },
        'opportunity-analysis': { title: '商机分析', icon: 'trending-up' },
        'risk-assessment': { title: '风险评估', icon: 'alert-triangle' }
      }
      return functions[functionType] || { title: 'AI分析', icon: 'zap' }
    },

    generateMockResult(functionType) {
      const results = {
        'reply-suggestion': this.email ? '建议回复：感谢您的询价，我们将在24小时内为您提供详细的报价方案。如有任何疑问，请随时联系我们。' : '智能回复功能可以根据邮件内容生成专业的回复建议，提高沟通效率。',
        'tone-analysis': this.email ? '语气分析：客户语气友好且专业，表现出强烈的合作意向。建议采用积极正面的回复语气。' : '语气分析功能可以识别邮件中的情感倾向，帮助您选择合适的回复语气。',
        'follow-up': this.email ? '跟进建议：建议在3个工作日内进行跟进，最佳跟进时间为工作日上午10:00-11:00。' : '智能跟进功能可以根据客户行为模式，推荐最佳的跟进时机。',
        'template-match': this.email ? '推荐模板：商务询价回复模板，包含产品介绍、价格说明和后续流程。' : '模板匹配功能可以根据邮件类型，自动推荐合适的回复模板。',
        'customer-profile': '客户画像分析：基于历史数据和行为模式，为您提供全面的客户特征分析，包括偏好、决策习惯和商业价值评估。',
        'interaction-history': '互动历史分析：整理和分析与客户的所有互动记录，识别沟通模式和关键节点，为后续跟进提供参考。',
        'opportunity-analysis': '商机分析：运用AI算法分析客户需求和市场趋势，识别潜在商业机会，评估成交概率和预期价值。',
        'risk-assessment': '风险评估：综合分析客户行为、市场环境和竞争态势，评估客户流失风险并提供预防建议。'
      }
      return results[functionType] || '分析完成，请查看详细结果。'
    },

    closeResult() {
      this.showResult = false
      this.resultContent = ''
    },

    showToast(message, type = 'info') {
      // 这里可以触发全局toast组件
      console.log(`${type}: ${message}`)
    }
  }
}
</script>

<style scoped>
.ai-assistant-drawer {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: white;
  border-left: 1px solid #e4e7ed;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  transition: right 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.ai-assistant-drawer.visible {
  right: 0;
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
}

.header-title .icon-small {
  margin-right: 8px;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.drawer-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.ai-module {
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.module-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  cursor: pointer;
  transition: background-color 0.2s;
}

.module-header:hover {
  background: #e9ecef;
}

.module-header span {
  flex: 1;
  margin-left: 8px;
  font-weight: 500;
}

.expand-icon {
  transition: transform 0.2s;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.module-content {
  padding: 8px;
}

.ai-function {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 4px;
}

.ai-function:hover {
  background: #f0f4ff;
}

.function-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 12px;
}

.function-info {
  flex: 1;
}

.function-title {
  font-weight: 500;
  font-size: 14px;
  color: #303133;
  margin-bottom: 2px;
}

.function-desc {
  font-size: 12px;
  color: #909399;
}

.ai-result {
  margin-top: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  overflow: hidden;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 500;
  font-size: 14px;
}

.result-title {
  display: flex;
  align-items: center;
}

.result-title .icon-small {
  margin-right: 8px;
}

.close-result-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-result-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.result-content {
  padding: 16px;
}

.result-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 14px;
}

.result-loading .icon-small {
  margin-right: 8px;
}

.result-text {
  font-size: 14px;
  line-height: 1.6;
  color: #303133;
  background: white;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spin {
  animation: spin 1s linear infinite;
}

.icon-small {
  width: 16px;
  height: 16px;
}

.icon-tiny {
  width: 12px;
  height: 12px;
}
</style>
