<template>
  <div class="ai-assistant-drawer" :class="{ 'visible': visible }">
    <div class="drawer-header">
      <div class="header-title">
        <zap-icon class="icon-small" />
        AI 助手
      </div>
      <button class="close-btn" @click="$emit('close')">
        <x-icon class="icon-tiny" />
      </button>
    </div>

    <div class="drawer-content">
      <!-- 智能沟通模块 -->
      <div class="ai-module">
        <div class="module-header" @click="toggleModule('communication')">
          <div>
            <message-circle-icon class="icon-small" />
            <span>智能沟通</span>
          </div>
          <div class="module-subtitle">让每次沟通都更高效、更聪明</div>
        </div>

        <div class="module-content" v-show="expandedModules.communication">
          <div class="ai-function" @click="handleFunction('email-summary')">
            <div class="function-icon email-summary">
              <file-text-icon class="icon-small" />
            </div>
            <div class="function-info">
              <div class="function-title">智能邮件摘要</div>
              <div class="function-desc">提取关键信息，节省人工阅读</div>
            </div>
          </div>

          <div class="ai-function" @click="handleFunction('ai-writing')">
            <div class="function-icon ai-writing">
              <edit-icon class="icon-small" />
            </div>
            <div class="function-info">
              <div class="function-title">AI写信</div>
              <div class="function-desc">突破语言瓶颈，创作高质量邮件</div>
            </div>
          </div>

          <div class="ai-function" @click="handleFunction('ai-polish')">
            <div class="function-icon ai-polish">
              <sparkles-icon class="icon-small" />
            </div>
            <div class="function-info">
              <div class="function-title">AI润色</div>
              <div class="function-desc">提高邮件质量，沟通更高效</div>
            </div>
          </div>

          <div class="ai-function" @click="handleFunction('ai-reply')">
            <div class="function-icon ai-reply">
              <reply-icon class="icon-small" />
            </div>
            <div class="function-info">
              <div class="function-title">AI智能回复</div>
              <div class="function-desc">提供高质量的回答和解决方案</div>
            </div>
          </div>

          <div class="ai-function" @click="handleFunction('whatsapp-summary')">
            <div class="function-icon whatsapp-summary">
              <message-square-icon class="icon-small" />
            </div>
            <div class="function-info">
              <div class="function-title">WhatsApp智能摘要</div>
              <div class="function-desc">提取关键信息，节省人工阅读</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 客户洞察模块 -->
      <div class="ai-module">
        <div class="module-header" @click="toggleModule('insights')">
          <div>
            <search-icon class="icon-small" />
            <span>客户洞察</span>
          </div>
          <div class="module-subtitle">深入了解您的客户，数据驱动业务决策</div>
        </div>

        <div class="module-content" v-show="expandedModules.insights">
          <div class="ai-function" @click="handleFunction('image-analysis')">
            <div class="function-icon image-analysis">
              <image-icon class="icon-small" />
            </div>
            <div class="function-info">
              <div class="function-title">智能图像分析</div>
              <div class="function-desc">深入了解用户，提供市场洞察</div>
            </div>
          </div>

          <div class="ai-function" @click="handleFunction('report-analysis')">
            <div class="function-icon report-analysis">
              <bar-chart-icon class="icon-small" />
            </div>
            <div class="function-info">
              <div class="function-title">智能报表分析</div>
              <div class="function-desc">更直观、准确和高效的数据分析</div>
            </div>
          </div>
        </div>
      </div>

      <!-- AI分析结果显示区域 -->
      <div class="ai-result" v-if="showResult">
        <div class="result-header">
          <div class="result-title">
            <zap-icon class="icon-small" />
            {{ currentFunction.title }}
          </div>
          <button class="close-result-btn" @click="closeResult">
            <x-icon class="icon-tiny" />
          </button>
        </div>
        <div class="result-content">
          <div v-if="isLoading" class="result-loading">
            <rotate-cw-icon class="icon-small spin" />
            正在分析...
          </div>
          <div v-else class="result-text">
            {{ resultContent }}
          </div>

          <!-- 底部按钮 -->
          <div v-if="showBottomButton" class="result-bottom-button">
            <button class="bottom-btn" @click="handleBottomButtonClick">
              智能图像分析
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  Zap, X, MessageCircle, ChevronDown, Reply, FileText, Edit, Sparkles,
  MessageSquare, Search, Image, BarChart, RotateCw
} from 'lucide-vue'

export default {
  name: 'AiAssistantDrawer',
  components: {
    ZapIcon: Zap,
    XIcon: X,
    MessageCircleIcon: MessageCircle,
    ChevronDownIcon: ChevronDown,
    ReplyIcon: Reply,
    FileTextIcon: FileText,
    EditIcon: Edit,
    SparklesIcon: Sparkles,
    MessageSquareIcon: MessageSquare,
    SearchIcon: Search,
    ImageIcon: Image,
    BarChartIcon: BarChart,
    RotateCwIcon: RotateCw
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    email: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      expandedModules: {
        communication: true,
        insights: false
      },
      showResult: false,
      isLoading: false,
      resultContent: '',
      currentFunction: {},
      // 添加底部按钮状态
      showBottomButton: false
    }
  },
  methods: {
    toggleModule(module) {
      this.expandedModules[module] = !this.expandedModules[module]
    },

    async handleFunction(functionType) {
      // 检查是否需要邮件上下文的功能
      const emailRequiredFunctions = ['email-summary', 'ai-reply', 'whatsapp-summary']
      if (!this.email && emailRequiredFunctions.includes(functionType)) {
        this.showToast('此功能需要在邮件模块中使用', 'warning')
        return
      }

      this.currentFunction = this.getFunctionInfo(functionType)
      this.showResult = true
      this.isLoading = true

      try {
        // 根据功能类型调用不同的API
        let result = ''
        switch (functionType) {
          case 'email-summary':
            result = await this.callEmailSummaryAPI()
            break
          case 'ai-writing':
            result = await this.callAiWritingAPI()
            break
          case 'ai-polish':
            result = await this.callAiPolishAPI()
            break
          case 'ai-reply':
            result = await this.callAiReplyAPI()
            break
          case 'whatsapp-summary':
            result = await this.callWhatsAppSummaryAPI()
            break
          case 'image-analysis':
            result = await this.callImageAnalysisAPI()
            break
          case 'report-analysis':
            result = await this.callReportAnalysisAPI()
            break
          default:
            result = this.generateMockResult(functionType)
        }

        this.resultContent = result

        // 对于某些功能，显示底部按钮
        if (['image-analysis'].includes(functionType)) {
          this.showBottomButton = true
        }
      } catch (error) {
        this.resultContent = '分析失败，请稍后重试'
      } finally {
        this.isLoading = false
      }
    },

    getFunctionInfo(functionType) {
      const functions = {
        'email-summary': { title: '智能邮件摘要', icon: 'file-text' },
        'ai-writing': { title: 'AI写信', icon: 'edit' },
        'ai-polish': { title: 'AI润色', icon: 'sparkles' },
        'ai-reply': { title: 'AI智能回复', icon: 'reply' },
        'whatsapp-summary': { title: 'WhatsApp智能摘要', icon: 'message-square' },
        'image-analysis': { title: '智能图像分析', icon: 'image' },
        'report-analysis': { title: '智能报表分析', icon: 'bar-chart' }
      }
      return functions[functionType] || { title: 'AI分析', icon: 'zap' }
    },

    generateMockResult(functionType) {
      const results = {
        'email-summary': this.email ?
          '邮件摘要：客户询问产品价格和交付时间，表达了强烈的购买意向，建议尽快回复并提供详细报价。' :
          '智能邮件摘要功能可以快速提取邮件关键信息，节省阅读时间。',
        'ai-writing': '基于您的需求，AI已为您生成了一份专业的商务邮件模板，包含问候、正文和结尾部分。',
        'ai-polish': '已对您的邮件进行润色优化，提升了语言表达的专业性和准确性，使沟通更加高效。',
        'ai-reply': this.email ?
          '智能回复建议：感谢您的询价，我们的产品具有优异的性能和竞争力的价格，期待与您进一步洽谈。' :
          'AI智能回复功能可以根据邮件内容生成高质量的回复建议。',
        'whatsapp-summary': '已提取WhatsApp对话的关键信息，包括客户需求、关注点和后续行动建议。',
        'image-analysis': '图像分析完成：识别出产品图片中的关键特征，分析了用户偏好和市场趋势，为您提供深入的市场洞察。',
        'report-analysis': '报表分析完成：数据显示销售趋势良好，客户满意度较高，建议继续优化产品质量和服务水平。'
      }
      return results[functionType] || '分析完成，请查看详细结果。'
    },

    closeResult() {
      this.showResult = false
      this.resultContent = ''
      this.showBottomButton = false
    },

    // API调用方法
    async callEmailSummaryAPI() {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      return this.generateMockResult('email-summary')
    },

    async callAiWritingAPI() {
      await new Promise(resolve => setTimeout(resolve, 2000))
      return this.generateMockResult('ai-writing')
    },

    async callAiPolishAPI() {
      await new Promise(resolve => setTimeout(resolve, 2000))
      return this.generateMockResult('ai-polish')
    },

    async callAiReplyAPI() {
      await new Promise(resolve => setTimeout(resolve, 2000))
      return this.generateMockResult('ai-reply')
    },

    async callWhatsAppSummaryAPI() {
      await new Promise(resolve => setTimeout(resolve, 2000))
      return this.generateMockResult('whatsapp-summary')
    },

    async callImageAnalysisAPI() {
      await new Promise(resolve => setTimeout(resolve, 2000))
      return this.generateMockResult('image-analysis')
    },

    async callReportAnalysisAPI() {
      await new Promise(resolve => setTimeout(resolve, 2000))
      return this.generateMockResult('report-analysis')
    },

    handleBottomButtonClick() {
      // 处理底部按钮点击事件
      this.showToast('智能图像分析功能已启动', 'success')
    },

    showToast(message, type = 'info') {
      // 这里可以触发全局toast组件
      console.log(`${type}: ${message}`)
    }
  }
}
</script>

<style scoped>
.ai-assistant-drawer {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: white;
  border-left: 1px solid #e4e7ed;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  transition: right 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.ai-assistant-drawer.visible {
  right: 0;
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
}

.header-title .icon-small {
  margin-right: 8px;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.drawer-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.ai-module {
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.module-header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 12px 16px;
  background: #f8f9fa;
  cursor: pointer;
  transition: background-color 0.2s;
}

.module-header:hover {
  background: #e9ecef;
}

.module-header > div:first-child {
  display: flex;
  align-items: center;
  width: 100%;
}

.module-header span {
  flex: 1;
  margin-left: 8px;
  font-weight: 500;
}

.module-subtitle {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  margin-left: 24px;
}

.expand-icon {
  transition: transform 0.2s;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.module-content {
  padding: 8px;
}

.ai-function {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 4px;
}

.ai-function:hover {
  background: #f0f4ff;
}

.function-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 12px;
}

/* 特定功能图标颜色 */
.function-icon.email-summary {
  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
}

.function-icon.ai-writing {
  background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%);
}

.function-icon.ai-polish {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.function-icon.ai-reply {
  background: linear-gradient(135deg, #2196f3 0%, #03a9f4 100%);
}

.function-icon.whatsapp-summary {
  background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
}

.function-icon.image-analysis {
  background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
}

.function-icon.report-analysis {
  background: linear-gradient(135deg, #ff5722 0%, #d84315 100%);
}

.function-info {
  flex: 1;
}

.function-title {
  font-weight: 500;
  font-size: 14px;
  color: #303133;
  margin-bottom: 2px;
}

.function-desc {
  font-size: 12px;
  color: #909399;
}

.ai-result {
  margin-top: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  overflow: hidden;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 500;
  font-size: 14px;
}

.result-title {
  display: flex;
  align-items: center;
}

.result-title .icon-small {
  margin-right: 8px;
}

.close-result-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-result-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.result-content {
  padding: 16px;
}

.result-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 14px;
}

.result-loading .icon-small {
  margin-right: 8px;
}

.result-text {
  font-size: 14px;
  line-height: 1.6;
  color: #303133;
  background: white;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.result-bottom-button {
  margin-top: 12px;
  text-align: center;
}

.bottom-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.bottom-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spin {
  animation: spin 1s linear infinite;
}

.icon-small {
  width: 16px;
  height: 16px;
}

.icon-tiny {
  width: 12px;
  height: 12px;
}
</style>
