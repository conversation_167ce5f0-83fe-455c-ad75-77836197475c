const getLocationOrigin = () => {
  return window.location.protocol + '//' + window.location.hostname + (window.location.port ? ':' + window.location.port : '')
}
const macUrl = '#'
const winUrl = '#'
const iOSUrl = '#'
const androidUrl = '#'

const registerUrl = '#'
const loginUrl = '#'
const contactUrl = '#' // 联系我们

const companyName = '奔达 - CRM'
const version = 'V12.0.0'
const baiduKey = 'LEcDcElRR6zFXoaG6jtANQYW'

const build = 20220110

// 默认表格样式
const tableStyle = {
  stripe: true, // 斑马纹
  class: [] // 'is-right-border-style', 'is-bottom-border-style'
}

export default {
  version,
  build,
  companyName,
  getLocationOrigin,
  baiduKey,
  macUrl,
  winUrl,
  iOSUrl,
  androidUrl,
  registerUrl,
  tableStyle,
  loginUrl,
  contactUrl
}
