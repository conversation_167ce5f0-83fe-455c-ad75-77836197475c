<template>
  <div class="email-tabs-container">
    <div class="tabs-header">
      <div
        v-for="(tab, index) in tabs"
        :key="index"
        class="tab"
        :class="{ 'active': activeTabIndex === index, 'inbox-tab': tab.type === 'inbox' }"
        @click="switchTab(index)"
      >
        <span class="tab-title">{{ tab.title }}</span>
        <button class="close-tab" @click.stop="closeTab(index)" v-if="index !== 0 || tabs.length > 1">
          <x-icon class="icon-tiny" />
        </button>
      </div>
    </div>

    <div class="tabs-content">
      <div
        v-for="(tab, index) in tabs"
        :key="index"
        class="tab-content"
        v-show="activeTabIndex === index"
      >
        <!-- 邮箱标签页 - 显示完整邮箱视图 -->
        <div v-if="tab.type === 'inbox'" class="inbox-tab-content">
          <slot name="inbox-view"></slot>
        </div>

        <!-- 邮件内容标签页 -->
        <fullscreen-email-view
          v-else
          :email="tab.email"
          :tags="tags"
          @close="closeTab(index)"
          @download-attachment="$emit('download-attachment', $event)"
          @print="$emit('print-email', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { X, Mail } from 'lucide-vue'
import FullscreenEmailView from './FullscreenEmailView.vue'

export default {
  name: 'EmailTabs',
  components: {
    XIcon: X,
    MailIcon: Mail,
    FullscreenEmailView
  },
  props: {
    tabs: {
      type: Array,
      default: () => []
    },
    tags: {
      type: Array,
      default: () => []
    },
    activeIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      activeTabIndex: 0
    }
  },
  watch: {
    tabs: {
      handler(newTabs) {
        // 如果没有标签页，重置激活的标签页索引
        if (newTabs.length === 0) {
          this.activeTabIndex = 0;
        }
        // 如果当前激活的标签页索引超出了标签页数量，设置为最后一个标签页
        else if (this.activeTabIndex >= newTabs.length) {
          this.activeTabIndex = newTabs.length - 1;
        }
      },
      deep: true
    },
    activeIndex: {
      handler(newIndex) {
        // 当父组件传入的activeIndex变化时，更新本地的activeTabIndex
        if (newIndex >= 0 && newIndex < this.tabs.length) {
          this.activeTabIndex = newIndex;
        }
      },
      immediate: true
    }
  },
  methods: {
    switchTab(index) {
      this.activeTabIndex = index;
      this.$emit('update:active-index', index);
    },
    closeTab(index) {
      this.$emit('close-tab', index);
    }
  }
}
</script>

<style scoped>
.email-tabs-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tabs-header {
  display: flex;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e6e9ed;
  overflow-x: auto;
  scrollbar-width: thin;
}

.tabs-header::-webkit-scrollbar {
  height: 4px;
}

.tabs-header::-webkit-scrollbar-thumb {
  background-color: #d9d9d9;
  border-radius: 4px;
}

.tab {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-right: 1px solid #e6e9ed;
  cursor: pointer;
  white-space: nowrap;
  max-width: 200px;
  transition: background-color 0.2s;
}

.tab:hover {
  background-color: #e6e9ed;
}

.tab.active {
  background-color: #fff;
  border-bottom: 2px solid #0052CC;
}

.tab-title {
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 8px;
  font-size: 14px;
}

.close-tab {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-tab:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.icon-tiny {
  width: 14px;
  height: 14px;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
}

/* 确保图标正确显示 */
::v-deep svg {
  width: 14px;
  height: 14px;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
}

.tabs-content {
  flex: 1;
  overflow: hidden;
}

.tab-content {
  height: 100%;
}

/* 邮箱标签页样式 */
.inbox-tab {
  background-color: #e6f7ff;
  border-bottom: 2px solid #1890ff;
}

.inbox-tab-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  overflow: auto;
}

.inbox-header {
  margin-bottom: 24px;
}

.inbox-header h2 {
  font-size: 24px;
  margin-bottom: 12px;
  color: #333;
}

.inbox-header p {
  color: #666;
  line-height: 1.6;
}

.inbox-actions {
  margin-top: 24px;
}

.action-button {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.action-button:hover {
  background-color: #40a9ff;
}
</style>
